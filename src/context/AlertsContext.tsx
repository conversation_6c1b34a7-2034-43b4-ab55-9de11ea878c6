import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import axiosConfig from "../services/axiosConfig";

export interface UnreadMessage {
  id: string;
  consultation_id: string;
  sent_at: string;
  message: string;
}

export interface Alert {
  consultationId: string;
  lastMessage: UnreadMessage;
}

export interface AlertsContextValue {
  alerts: Alert[] | null;
  count: number;
  loading: boolean;
  error: Error | null;
  refreshAlerts: () => void;
}

const AlertsContext = createContext<AlertsContextValue | undefined>(undefined);

export const AlertsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [alerts, setAlerts] = useState<Alert[] | null>(null);
  const [count, setCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchAlerts = useCallback(async () => {
    if (loading) return;
    setLoading(true);
    try {
      const response = await axiosConfig.get<{
        messages: Alert[];
        count: number;
      }>("/user/unread-messages");
      setAlerts(response.data.messages);
      setCount(response.data.count);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch alerts on mount.
  useEffect(() => {
    fetchAlerts();
  }, [fetchAlerts]);

  const refreshAlerts = () => {
    fetchAlerts();
  };

  return (
    <AlertsContext.Provider
      value={{ alerts, count, loading, error, refreshAlerts }}
    >
      {children}
    </AlertsContext.Provider>
  );
};

export const useAlertsContext = (): AlertsContextValue => {
  const context = useContext(AlertsContext);
  if (context === undefined) {
    throw new Error("useAlertsContext must be used within an AlertsProvider");
  }
  return context;
};
