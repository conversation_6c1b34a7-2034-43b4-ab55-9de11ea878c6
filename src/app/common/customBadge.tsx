import React from "react";
import { YStack, Text } from "tamagui";

interface CustomBadgeProps {
  count: number;
}

const CustomBadge = ({ count }: CustomBadgeProps) => {
  return (
    <YStack
      style={{
        position: "absolute",
        top: -5,
        right: -5,
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: "red",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Text color="white" fontSize={10}>
        {count}
      </Text>
    </YStack>
  );
};

export default CustomBadge;
