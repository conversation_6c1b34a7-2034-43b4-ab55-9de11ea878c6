import { useLoginStyles } from "@/styles/LoginStyle";
import { useState } from "react";
import {
  Alert,
  Dimensions,
  Linking,
  Pressable,
  Modal,
  TextInput,
  StyleSheet,
  View as RNView,
} from "react-native";
import axiosConfig from "~/services/axiosConfig";
import EmailAndPassword from "src/components/EmailAndPassword";
import { Button, Image, Text, View, YStack, Spinner, XStack } from "tamagui";
import { useTheme } from "@/_layout";
import { useAuth } from "~/context/AuthContext";
import { Dot, Info } from "@tamagui/lucide-icons";
import FacilityDrawer from "src/components/FacilityDrawer";
import { VersionInfoModal } from "src/components/VersionInfoModal";

export default function Login() {
  const { width } = Dimensions.get("window");
  const imageWidth = width * 0.6;
  const { signIn, signInWithPCC } = useAuth();
  const loginStyles = useLoginStyles();
  const [signingIn, setSigningIn] = useState(false);
  const [error, setError] = useState("");
  const [loggingInWithPCC, setLoggingInWithPCC] = useState(false);

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const [showResetModal, setShowResetModal] = useState(false);
  const [resetEmail, setResetEmail] = useState("");
  const [resetLoading, setResetLoading] = useState(false);

  const [showEmailPassword, setShowEmailPassword] = useState(false);
  const [showDisplayModal, setShowDisplayModal] = useState(false);
  const [showVersionModal, setShowVersionModal] = useState(false);
  const [isDropdownOpen, setDropdownOpen] = useState(false);
  const { setTheme } = useTheme();
  const themeValues = [
    { name: "System Settings", id: "1" },
    { name: "Light Theme", id: "2" },
    { name: "Dark Theme", id: "3" },
  ];

  const setBackgroundTheme = (id: string) => {
    if (id === "1") {
      setTheme("system");
    } else if (id === "2") {
      setTheme("light");
    } else if (id === "3") {
      setTheme("dark");
    }
  };

  const handleSignIn = async () => {
    setSigningIn(true);
    const result = await signIn(email, password);
    if (!result.success) {
      setError(result.error ?? "An error occurred");
      setSigningIn(false);
    }
  };

  const handlePCCLogin = async () => {
    setLoggingInWithPCC(true);
    const result = await signInWithPCC();
    if (!result.success) {
      setLoggingInWithPCC(false);
      console.error(result.error);
      // TODO: Display an error message if needed
    }
  };

  const handleHelpPress = () => {
    const url = "https://www.vitalcare.org/contact";
    Linking.openURL(url).catch((err) =>
      console.error("Error opening URL:", err)
    );
  };

  const handlePrivacyPress = () => {
    const url = "https://www.vitalcare.org/privacy-policy";
    Linking.openURL(url).catch((err) =>
      console.error("Error opening URL:", err)
    );
  };

  const handleTermsPress = () => {
    const url = "https://www.vitalcare.org/terms-of-service-2";
    Linking.openURL(url).catch((err) =>
      console.error("Error opening URL:", err)
    );
  };

  const handleDisplayPress = () => {
    // show modal to change display settings
    setShowDisplayModal(true);
  };

  const handleVersionPress = () => {
    setShowVersionModal(true);
  };

  const handleSendReset = async () => {
    if (!resetEmail) {
      Alert.alert("Please enter your email.");
      return;
    }
    setResetLoading(true);
    try {
      await axiosConfig.post("/auth/forgot-password", { email: resetEmail });
      Alert.alert(
        "If an account exists, a reset link has been sent to your email."
      );
      setShowResetModal(false);
      setResetEmail("");
    } catch (err) {
      console.error(err);
      Alert.alert("Error sending reset link.");
    } finally {
      setResetLoading(false);
    }
  };

  if (loggingInWithPCC) {
    return (
      <YStack
        style={{
          height: 300,
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Spinner size="large" color="rgba(21, 112, 239, 1)" />
      </YStack>
    );
  }

  return (
    <View {...loginStyles.parent}>
      <YStack {...loginStyles.header}>
        <XStack
          style={{
            width: "100%",
            justifyContent: "center",
            alignItems: "center",
            position: "relative",
          }}
        >
          <Image
            source={require("../../assets/images/vital-care-login-logo.png")}
            {...loginStyles.image(imageWidth)}
          />
          <Pressable
            onPress={handleVersionPress}
            style={{
              position: "absolute",
              right: 20,
              padding: 8,
            }}
          >
            <Info size={24} color="$textcolor" />
          </Pressable>
        </XStack>
        <Text {...loginStyles.title}>Log in</Text>
        <Text {...loginStyles.subtitle} justify="center">
          Welcome back! Please enter your details.
        </Text>
      </YStack>

      <YStack style={{ height: "100%", flex: 1 }} {...loginStyles.container}>
        {!showEmailPassword && (
          <Pressable onPress={handlePCCLogin} {...loginStyles.pccButton}>
            <Image
              source={require("../../assets/images/pcc-sign-in.png")}
              {...loginStyles.pccIcon}
            />
          </Pressable>
        )}
        {showEmailPassword ? (
          <>
            <YStack {...loginStyles.formContainer}>
              <EmailAndPassword
                email={email}
                password={password}
                setEmail={setEmail}
                setPassword={setPassword}
              />
            </YStack>

            {error ? (
              <YStack>
                <Text color="red" fontSize={14}>
                  {error}
                </Text>
              </YStack>
            ) : null}

            <YStack>
              <XStack {...loginStyles.rememberContainer}>
                <XStack>
                  <Pressable onPress={() => setShowResetModal(true)}>
                    <Text {...loginStyles.forgotPassword}>Forgot Password</Text>
                  </Pressable>
                </XStack>
              </XStack>
            </YStack>

            <YStack {...loginStyles.buttonContainer}>
              <Button
                {...loginStyles.signInButton}
                onPress={handleSignIn}
                disabled={signingIn}
                style={{
                  maxWidth: 300,
                  width: "100%",
                  margin: "auto",
                }}
              >
                {signingIn ? (
                  <Spinner size="small" color="$buttonWhiteColor" />
                ) : (
                  "Sign in"
                )}
              </Button>
              <Button
                {...loginStyles.backButton}
                onPress={() => setShowEmailPassword(false)}
                disabled={signingIn}
                style={{
                  maxWidth: 300,
                  width: "100%",
                  margin: "auto",
                }}
              >
                Back
              </Button>
            </YStack>
          </>
        ) : (
          <YStack {...loginStyles.buttonContainer}>
            <Button
              {...loginStyles.signInButton}
              onPress={() => setShowEmailPassword(true)}
              style={{
                maxWidth: 290,
                width: "100%",
                margin: "auto",
              }}
            >
              Email/Username Login
            </Button>
            <Button
              {...loginStyles.signInButton}
              onPress={() => setShowEmailPassword(true)}
              style={{
                maxWidth: 290,
                width: "100%",
                margin: "auto",
              }}
            >
              Provider Login
            </Button>
          </YStack>
        )}
      </YStack>
      <YStack
        style={{
          display: "flex",
          alignItems: "center",
          flexDirection: "row",
          justifyContent: "center",
        }}
      >
        <Text onPress={handleHelpPress}>Help</Text>
        <Dot size="$2" />
        <Text onPress={handlePrivacyPress}>Privacy Policy</Text>
        <Dot size="$2" />
        <Text onPress={handleTermsPress}>Terms</Text>
        <Dot size="$2" />
        <Text onPress={handleDisplayPress}>Display</Text>
      </YStack>
      <Modal
        visible={showResetModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowResetModal(false)}
      >
        <RNView style={styles.modalOverlay}>
          <RNView style={styles.modalContainer}>
            <Text fontSize={18} fontWeight="600">
              Reset Password
            </Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              value={resetEmail}
              onChangeText={setResetEmail}
              autoCapitalize="none"
              keyboardType="email-address"
            />
            <Button
              {...loginStyles.signInButton}
              onPress={handleSendReset}
              disabled={resetLoading}
              style={{
                maxWidth: 300,
                width: "100%",
                margin: "auto",
              }}
            >
              {resetLoading ? (
                <Spinner size="small" />
              ) : (
                "Send password reset link"
              )}
            </Button>
            <Pressable onPress={() => setShowResetModal(false)}>
              <Text {...loginStyles.forgotPassword} style={{ marginTop: 10 }}>
                Close
              </Text>
            </Pressable>
          </RNView>
        </RNView>
      </Modal>
      <Modal
        visible={showDisplayModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowDisplayModal(false)}
      >
        <RNView style={styles.modalOverlay}>
          <RNView style={styles.modalContainer}>
            <Text fontSize={18} fontWeight="600" mb={20} color="$primaryColor">
              Appearance
            </Text>
            <FacilityDrawer
              data={themeValues}
              placeholder="System Settings"
              onSelect={(id: string) => setBackgroundTheme(id)}
              onOpen={() => setDropdownOpen(true)}
              onClose={() => setDropdownOpen(false)}
            />
            <Pressable onPress={() => setShowDisplayModal(false)}>
              <Text {...loginStyles.forgotPassword} style={{ marginTop: 10 }}>
                Close
              </Text>
            </Pressable>
          </RNView>
        </RNView>
      </Modal>
      <Modal visible={showVersionModal} transparent animationType="fade">
        <RNView
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            width: "100%",
            height: "100%",
          }}
        >
          <VersionInfoModal
            open={showVersionModal}
            onClose={setShowVersionModal}
          />
        </RNView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: "80%",
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 8,
    alignItems: "center",
  },
  input: {
    width: "100%",
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 4,
    padding: 8,
    marginVertical: 12,
  },
});
