import React from "react";
import { Pressable } from "react-native";
import { XStack } from "tamagui";
import FontAwesome6 from "@expo/vector-icons/FontAwesome6";
import CustomBadge from "./customBadge";
import { useRouter } from "expo-router";
import { useAlertsContext } from "src/context/AlertsContext";
import { useAuth } from "~/context/AuthContext";

const UserAlerts = () => {
  const router = useRouter();
  const { count } = useAlertsContext();
  const { user } = useAuth();
  const role = user?.role;

  return (
    <XStack position="relative" style={{ marginRight: 18 }}>
      <Pressable
        onPress={() => {
          if (!role) return;
          router.push(`/${role}/messages`);
        }}
      >
        <FontAwesome6 name="bell" size={30} color="black" />
        {count > 0 && <CustomBadge count={count} />}
      </Pressable>
    </XStack>
  );
};

export default UserAlerts;
