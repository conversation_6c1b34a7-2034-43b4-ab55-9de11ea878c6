import { Button, Image, Text, TextArea, YStack, XStack } from "tamagui";
import { Pen, PenOff } from "@tamagui/lucide-icons"; // Import Pen icon
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Dimensions, Keyboard } from "react-native";
import { useEffect, useState, useRef } from "react";
import axios from "../../services/axiosConfig";
type NotesProps = {
  notes: string;
  setNotes: (text: string) => void;
  isSubmitted: boolean;
  consultationId: string;
  isClicked: boolean;
  setIsClicked: (clicked: boolean) => void;
  setDialogOpen?: (open: boolean) => void;
  setDialogContent?: (content: {
    title: string;
    body: string;
    btnText: string;
  }) => void;
  generateSummary: boolean;
  setGenerateSummary: (generateSummary: boolean) => void;
};

export default function Notes({
  notes,
  setNotes,
  isSubmitted = false,
  consultationId,
  isClicked = false,
  setIsClicked = () => {},
  setDialogOpen = () => {},
  setDialogContent = ({
    title,
    body,
    btnText,
  }: {
    title: string;
    body: string;
    btnText: string;
  }) => {},
  generateSummary = false,
  setGenerateSummary,
}: NotesProps) {
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get("window").height;
  const screenWidth = Dimensions.get("window").width;
  const availableHeight = screenHeight - (insets.top + insets.bottom);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [isEditable, setIsEditable] = useState(!isSubmitted);
  const textAreaRef = useRef(null);

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (generateSummary) {
      generateAISummary();
    }
  }, [generateSummary]);

  const generateSummaryConfirmation = () => {
    setDialogContent({
      title: "Generate AI Summary",
      body: "Generating AI summary will result in loss of your written notes if any , Are you sure you want to continue?",
      btnText: "OK",
    });
    setDialogOpen(true);
  };

  const generateAISummary = async () => {
    try {
      setGenerateSummary(false);
      setLoading(true);
      Keyboard.dismiss();
      const response = await axios.post("/billing/ai-summary-generate", {
        consultationId: consultationId,
      });
      if (response.status === 200) {
        setNotes(response.data.aiBillingNote);
      }
      setIsClicked(true);
    } catch (error: any) {
      console.log("Error while generating summary: ", error);

      const errorDetails = error?.response?.data?.details;
      setDialogContent({
        title: "Error generating summary",
        body:
          errorDetails + ". Update the Required fields and try again later. " ||
          "Please try again.",
        btnText: "OK",
      });
      setDialogOpen(true);
    } finally {
      setGenerateSummary(false);
      setLoading(false);
    }
  };

  useEffect(() => {
    const showSub = Keyboard.addListener("keyboardDidShow", () =>
      setKeyboardVisible(true)
    );
    const hideSub = Keyboard.addListener("keyboardDidHide", () =>
      setKeyboardVisible(false)
    );
    return () => {
      showSub.remove();
      hideSub.remove();
    };
  }, []);

  const notesStyles = useNotesStyles(
    availableHeight,
    screenWidth,
    keyboardVisible
  );

  const handlePenButtonPress = () => {
    setIsEditable((prev) => {
      const newEditable = !prev;
      return newEditable;
    });
  };

  return (
    <YStack {...notesStyles.mainContainer}>
      <Text {...notesStyles.notesText}>Notes</Text>
      <XStack width="100%">
        <Button
          disabled={isSubmitted}
          {...notesStyles.generateSummaryBtn}
          icon={
            <Image
              source={require("../../assets/images/star.png")}
              {...notesStyles.starIcon}
            />
          }
          width="85%"
          onPress={generateSummaryConfirmation}
        >
          Generate Summary
        </Button>
        <Button
          disabled={isSubmitted}
          {...notesStyles.generateSummaryBtn}
          icon={isEditable ? <Pen size="$1" /> : <PenOff size="$1" />}
          width="10%"
          marginInlineStart={10}
          onPress={handlePenButtonPress}
        />
      </XStack>
      <YStack {...notesStyles.textAreaContainer}>
        <TextArea
          ref={textAreaRef}
          {...notesStyles.textArea}
          placeholder={loading ? "" : "Start Writing Notes"} // Hide placeholder while loading
          placeholderTextColor={"$textcolor"}
          overflow="hidden"
          returnKeyType="done"
          blurOnSubmit={true}
          onSubmitEditing={Keyboard.dismiss}
          multiline={true}
          value={notes}
          onChangeText={setNotes}
          editable={isEditable && !isSubmitted && !loading} // Prevent typing while loading
        />
        {loading && (
          <YStack {...notesStyles.generateSummaryLoader}>
            <Text color="$textcolor">Generating Summary...</Text>
            {/* You can replace this with a Tamagui Spinner */}
            <Text mt="$2">⏳</Text>
          </YStack>
        )}
      </YStack>
    </YStack>
  );
}

export const useNotesStyles = (
  availableHeight: number,
  screenWidth: number,
  keyboardVisible: boolean
) => {
  const minimizedHeight = availableHeight * 0.27;
  const defaultMinHeight = availableHeight * 0.3;
  const defaultMaxHeight = availableHeight * 0.5;

  return {
    mainContainer: {
      flex: 1,
    },
    notesText: {
      fontSize: 20,
      fontWeight: "600" as any,
      marginBottom: 10,
    },
    generateSummaryBtn: {
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$confirmOrderBlue" as any,
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
      marginBlockStart: 10,
      alignSelf: "center",
      paddingHorizontal: 20,
    },
    starIcon: {
      width: 20,
      height: 20,
    },
    textAreaContainer: {
      flex: 1,
      marginBlockStart: 20,
      width: "100%" as any,
    },
    textArea: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderRadius: 7,
      borderColor: "$primaryBorderColor" as any,
      fontWeight: "300" as any,
      color: "$textcolor" as any,
      padding: 10,
      fontSize: 14,
      textAlignVertical: "top" as any,
      flex: 1,
      minHeight: keyboardVisible ? minimizedHeight : defaultMinHeight,
      maxHeight: keyboardVisible ? minimizedHeight : defaultMaxHeight,
    },
    generateSummaryLoader: {
      position: "absolute" as any,
      top: 10 as any,
      left: 10 as any,
      right: 10 as any,
      bottom: 10 as any,
      justifyContent: "center" as any,
      alignItems: "center" as any,
      backgroundColor: "$screenBackgroundcolor" as any,
      borderRadius: 7,
      opacity: 0.8,
    },
  };
};
