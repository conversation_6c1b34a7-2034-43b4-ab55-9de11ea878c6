import ScreenHeader from "src/components/ScreenHeader";
import { <PERSON>, Text, YStack, Button, Spinner } from "tamagui";
import { useReviewCallStyles } from "./Styles/ReviewCallStyle";
import { Modal, TouchableOpacity } from "react-native";
import { useLocalSearchParams, useNavigation, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import SheetDemo from "src/components/SettingsDrawer";
import Billing from "./billing";
import Notes from "./notes";
import Profile from "./profile";
import Chat from "./chat";
import { PenLine, Save } from "@tamagui/lucide-icons";
import axiosConfig from "~/services/axiosConfig";
import { DialogBox } from "src/components/Dialog";
import { useConsultationV2 } from "src/hooks/useCOnsultationV2";
import { CommonActions } from "@react-navigation/native";
import { PhysicalExam } from "src/types/consultationV2";
import { setShouldRefetchConsultations } from "src/globals/consultationFlag";

export default function ReviewCall() {
  const { consultationId, openChat } = useLocalSearchParams();
  const shouldOpenChat = openChat === "true";
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const reviewCallStyles = useReviewCallStyles();
  const navigation = useNavigation();
  // Local state management instead of using context.
  const [notes, setNotes] = useState("");
  const [ordersText, setOrdersText] = useState("");
  const [selectedCodes, setSelectedCodes] = useState<
    { code: string; description: string }[]
  >([]);
  const [selectedBillingCodes, setSelectedBillingCodes] = useState<
    { code: string; description: string }[]
  >([]);
  const [patientDetailsSnapshot, setPatientDetailsSnapshot] =
    useState<any>(null);
  const [isClicked, setIsClicked] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [generateSummary, setGenerateSummary] = useState(false);
  const [dialogContent, setDialogContent] = useState({
    title: "",
    body: "",
    btnText: "Close",
  });
  const [isSubmitted, setSubmitted] = useState(false);
  const [physicalExam, setPhysicalExam] = useState<PhysicalExam | null>(null);

  const { consultation, loading, error, refresh } = useConsultationV2(
    consultationId as string
  );
  const [reviewConsultation, setReviewConsultation] = useState<any>(null);
  useEffect(() => {
    if (consultation) {
      setSubmitted(consultation.status === "submitted");
      setOrdersText(consultation.order || "");
      setPatientDetailsSnapshot(consultation.patient_details_snapshot || {});
      setNotes(consultation.notes || "");
      if (consultation.billing_codes) {
        setSelectedBillingCodes(consultation.billing_codes);
      }
      if (consultation.icd_codes) {
        setSelectedCodes(consultation.icd_codes);
      }
    }
    if (consultation?.physical_exam) {
      setPhysicalExam(consultation.physical_exam);
    }

    const time = new Date(consultation?.created_at || "").toLocaleTimeString(
      [],
      {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      }
    );

    const date = new Date(consultation?.created_at || "").toLocaleDateString();

    const tempConsultation: any = {
      id: consultation?.id || "",
      full_name: `${consultation?.patient_details_snapshot?.full_name}`,
      gender: consultation?.patient_details_snapshot?.gender || "",
      dob: consultation?.patient_details_snapshot?.dateOfBirth || "",
      date: date,
      time: time,
      chief_complaint: consultation?.chief_complaint || "",
      order: consultation?.order || "",
      status: consultation?.status || "",
    };
    setReviewConsultation(tempConsultation);
  }, [consultation]);

  const onFinishLaterDialog = () => {
    if (dialogContent?.title === "Success") {
      refresh();
    }
    setDialogOpen(false);
  };

  const generateAISummary = () => {
    if (
      activeTab === "Notes" &&
      dialogContent?.title === "Generate AI Summary"
    ) {
      setDialogOpen(false);
      setGenerateSummary(true);
    }
  };

  const clearStackAndMove = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: "dashboard" }],
      })
    );
  };

  const validateBeforeSubmit = () => {
    if (!ordersText.trim()) {
      setDialogContent({
        title: "Missing Orders",
        body: "Please add at least one order before submitting.",
        btnText: "Add Order",
      });
      setDialogOpen(true);
      return false;
    }
    if (!notes.trim()) {
      setDialogContent({
        title: "Visit Notes Required",
        body: "All visit notes must be completed before submitting for billing.",
        btnText: "Add Notes",
      });
      setDialogOpen(true);
      return false;
    }
    if (!selectedCodes.length) {
      setDialogContent({
        title: "Billing Codes Required",
        body: "Please add the necessary billing codes.",
        btnText: "Add Codes",
      });
      setDialogOpen(true);
      return false;
    }
    if (!selectedBillingCodes.length) {
      setDialogContent({
        title: "ICD Codes Required",
        body: "Please add the necessary ICD codes.",
        btnText: "Add Codes",
      });
      setDialogOpen(true);
      return false;
    }
    return true;
  };

  const saveConsultation = async () => {
    try {
      setIsSaving(true);
      setTimeout(async () => {
        try {
          await axiosConfig.put(`/consultation/${consultationId}`, {
            order: ordersText,
            notes,
            billingCodes: selectedBillingCodes,
            icdCodes: selectedCodes,
            patientDetails: patientDetailsSnapshot,
            physical_exam: physicalExam,
          });
          setShouldRefetchConsultations(true);
          setDialogContent({
            title: "Success",
            body: "Consultation Saved SuccessFully",
            btnText: "OK",
          });
          setDialogOpen(true);
        } catch (error) {
          setDialogContent({
            title: "Error Saving Consultation",
            body: "Consultation not saved Please try again later",
            btnText: "OK",
          });
          setDialogOpen(true);
        } finally {
          setIsSaving(false);
        }
      }, 100);
    } catch (err) {
      setDialogContent({
        title: "Error Saving Consultation",
        body: "Consultation not saved Please try again later",
        btnText: "OK",
      });
      setDialogOpen(true);
      setIsSaving(false);
    }
  };

  const submitConsultation = async () => {
    if (!validateBeforeSubmit()) return;
    try {
      setIsSaving(true);
      setTimeout(async () => {
        try {
          try {
            await axiosConfig.put(`/consultation/${consultationId}`, {
              order: ordersText,
              notes,
              billingCodes: selectedBillingCodes,
              icdCodes: selectedCodes,
              patientDetails: patientDetailsSnapshot,
              physical_exam: physicalExam,
            });
            setShouldRefetchConsultations(true);
          } catch (err) {
            setDialogContent({
              title: "Error Saving Consultation",
              body: "Consultation not saved Please try again later",
              btnText: "OK",
            });
            setDialogOpen(true);
          }
          await axiosConfig.put(`/consultation/submit/${consultationId}`, {
            order: ordersText,
            notes,
            billingCodes: selectedCodes,
            icdCodes: selectedCodes,
            patientDetails: patientDetailsSnapshot,
            physical_exam: physicalExam,
          });
          setShouldRefetchConsultations(true);
          clearStackAndMove();
        } catch (error) {
          console.error("Error submitting consultation:", error);
        } finally {
          setIsSaving(false);
        }
      }, 500);
    } catch (err) {
      console.error("Unexpected error:", err);
      setIsSaving(false);
    }
  };

  const openSettings = () => setOpen(true);

  const navigateBack = () => {
    router.back();
  };

  const tabs = ["Notes", "Billing", "Profile", "Chat"] as const;
  const [activeTab, setActiveTab] = useState<
    "Notes" | "Billing" | "Profile" | "Chat"
  >(shouldOpenChat ? "Chat" : "Billing");

  const renderTab = (tab: (typeof tabs)[number]) => (
    <TouchableOpacity
      key={tab}
      style={[
        reviewCallStyles.tab,
        activeTab === tab && reviewCallStyles.activeTab,
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <Text
        style={[
          reviewCallStyles.tabText,
          activeTab === tab && reviewCallStyles.activeTabText,
        ]}
      >
        {tab}
      </Text>
    </TouchableOpacity>
  );

  if (loading || isSaving) {
    return (
      <View {...reviewCallStyles.container}>
        <YStack {...reviewCallStyles.centeredContent}>
          <Spinner size="large" />
        </YStack>
      </View>
    );
  }

  if (error) {
    return (
      <View {...reviewCallStyles.container}>
        <YStack {...reviewCallStyles.mainStack}>
          <ScreenHeader
            onAvatarPress={openSettings}
            screenName="Back"
            onBackPress={navigateBack}
          />
          <YStack {...reviewCallStyles.centeredContent}>
            <Text fontSize={20} color="$textcolor">
              Error fetching consultation data.
            </Text>
          </YStack>
        </YStack>
      </View>
    );
  }

  const patientName =
    patientDetailsSnapshot?.name ||
    `${patientDetailsSnapshot?.firstName || ""} ${patientDetailsSnapshot?.lastName || ""}`;

  return (
    <View {...reviewCallStyles.container}>
      <YStack {...reviewCallStyles.mainStack}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName={patientName}
          onBackPress={navigateBack}
        />
        <YStack {...reviewCallStyles.tabLayout}>
          <View>
            <View {...reviewCallStyles.tabContainer}>
              {tabs.map((tab, index) => (
                <React.Fragment key={tab}>
                  {renderTab(tab)}
                  {index < tabs.length - 1 && (
                    <View {...reviewCallStyles.divider} />
                  )}
                </React.Fragment>
              ))}
            </View>
          </View>
        </YStack>
        <YStack flex={1}>
          {activeTab === "Notes" && (
            <YStack flex={1}>
              <Notes
                notes={notes}
                setNotes={setNotes}
                isSubmitted={isSubmitted}
                consultationId={consultationId as string}
                isClicked={isClicked}
                setIsClicked={setIsClicked}
                setDialogOpen={setDialogOpen}
                setDialogContent={setDialogContent}
                generateSummary={generateSummary}
                setGenerateSummary={setGenerateSummary}
              />
            </YStack>
          )}
          {activeTab === "Billing" && (
            <YStack flex={1}>
              <Billing
                selectedCodes={selectedCodes}
                setSelectedCodes={setSelectedCodes}
                selectedBillingCodes={selectedBillingCodes}
                setSelectedBillingCodes={setSelectedBillingCodes}
                isSubmitted={isSubmitted}
              />
            </YStack>
          )}
          {activeTab === "Profile" && (
            <YStack flex={1}>
              <Profile
                onOrdersChange={setOrdersText}
                onChangePatientDetailsSnapshot={setPatientDetailsSnapshot}
                isSubmitted={isSubmitted}
                consultation={consultation}
                physicalExam={physicalExam}
                setPhysicalExam={setPhysicalExam}
              />
            </YStack>
          )}
          {activeTab === "Chat" && (
            <YStack flex={1}>
              <Text {...reviewCallStyles.chatText}>Chat</Text>
              <Chat />
            </YStack>
          )}
        </YStack>
        {(activeTab === "Notes" ||
          activeTab === "Billing" ||
          activeTab === "Profile") &&
          !isSubmitted && (
            <YStack {...reviewCallStyles.signInAndSaveContainer}>
              <Button
                {...reviewCallStyles.saveBtn}
                icon={<Save size={"$1"} />}
                onPress={saveConsultation}
              >
                Save
              </Button>
              {activeTab !== "Profile" && (
                <Button
                  {...reviewCallStyles.signInAndSaveBtn}
                  icon={<PenLine size={"$1"} />}
                  onPress={submitConsultation}
                >
                  Sign & Submit
                </Button>
              )}
            </YStack>
          )}
        <Modal visible={dialogOpen} transparent animationType="fade">
          <View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              width: "100%",
              height: "100%",
            }}
          >
            <DialogBox
              open={dialogOpen}
              onClose={setDialogOpen}
              title={dialogContent.title}
              body={dialogContent.body}
              btnText={dialogContent.btnText}
              onFinishLater={onFinishLaterDialog}
              showCloseBtn={
                dialogContent.title === "Generate AI Summary"
                  ? true
                  : ![
                      "Error generating summary",
                      "Error Saving Consultation",
                      "Success",
                    ].includes(dialogContent.title)
              }
              generateSummary={generateAISummary}
            />
          </View>
        </Modal>
        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
    </View>
  );
}
