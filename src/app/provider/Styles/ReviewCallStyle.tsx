import { useTheme } from "@/_layout";

export const useReviewCallStyles = () => {
  const { theme } = useTheme();
  const isDarktheme = theme === "dark";
  return {
    container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    mainStack: { flex: 1, marginBlock: 20, marginInline: 20 },
    tabLayout: { marginBlockStart: 20, marginBlockEnd: 20 },
    tabContainer: {
      flexDirection: "row" as any,
      borderRadius: 7,
      borderWidth: 1,
      borderColor: !isDarktheme ? "#D0D5DD" : ("#697586" as any),
      backgroundColor: "$screenBackgroundcolor" as any,
      overflow: "hidden" as any,
    },
    tab: {
      flex: 1,
      alignItems: "center" as any,
      justifyContent: "center" as any,
      paddingVertical: 12,
      backgroundColor: !isDarktheme ? "#FFFFFF" : "#0C0E12",
    },
    activeTab: {
      backgroundColor: !isDarktheme ? "#F9FAFB" : "#22262F",
    },
    tabText: {
      fontSize: 14,
      color: !isDarktheme ? "#344054" : "#94979C",
      fontWeight: 600,
    },
    activeTabText: {
      color: !isDarktheme ? "#1D2939" : "#FFFFFF",
    },
    divider: {
      width: 1,
      backgroundColor: "$primaryBorderColor" as any,
      height: "100%" as any,
      alignSelf: "center" as any,
    },
    contentContainer: {
      marginBlockStart: 5,
      backgroundColor: "$screenBackgroundcolor",
      flex: 1,
    },
    signInAndSaveContainer: {
      marginBlock: 5,
    },
    saveBtn: {
      backgroundColor: "$screenBackgroundcolor",
      color: "$textcolor" as any,
      fontSize: 16,
      size: "$4" as "$4",
      fontWeight: "600" as any,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
    },
    signInAndSaveBtn: {
      backgroundColor: "$primaryColor",
      color: "$buttonWhiteColor" as any,
      fontSize: 16,
      size: "$4" as "$4",
      fontWeight: "600" as any,
      borderWidth: 2,
    },
    chatText: {
      fontSize: 20,
      fontWeight: "600" as any,
      marginBottom: 20,
    },
    centeredContent: {
      flex: 1,
      justifyContent: "center" as any,
      alignItems: "center" as any,
    },
    buttonRow: {
      justifyContent: "space-between" as any,
      alignItems: "center" as any,
      marginBlockStart: 10,
    },
  };
};
