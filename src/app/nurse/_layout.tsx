import { Stack } from "expo-router";
import React from "react";
import InactivityWrapper from "../../components/InactivityWrapper"; // adjust the path accordingly
import { SocketProvider } from "~/context/NurseSocketContext";
import { AlertsProvider } from "~/context/AlertsContext";

export default function NurseLayout() {
  return (
    <InactivityWrapper>
      <AlertsProvider>
        <SocketProvider>
          <Stack
            screenOptions={{
              headerShown: false,
              contentStyle: { backgroundColor: "white" },
            }}
          >
            <Stack.Screen name="dashboard" options={{ headerShown: false }} />
            <Stack.Screen name="chat" options={{ headerShown: false }} />
            <Stack.Screen
              name="consultations"
              options={{ headerShown: false }}
            />
            <Stack.Screen name="new-visit" options={{ headerShown: false }} />
            <Stack.Screen name="scheduling" options={{ headerShown: false }} />
            <Stack.Screen name="settings" options={{ headerShown: false }} />
            <Stack.Screen name="transcript" options={{ headerShown: false }} />
            <Stack.Screen name="verify-pin" options={{ headerShown: false }} />
            <Stack.Screen
              name="calloverview"
              options={{ headerShown: false, gestureEnabled: false }}
            />
            <Stack.Screen
              name="telehealthconsent"
              options={{ headerShown: false }}
            />
            <Stack.Screen name="call" options={{ headerShown: false }} />
            <Stack.Screen
              name="messages"
              options={{
                headerShown: true,
                title: "Messages",
                headerTitleAlign: "center",
                headerTitleStyle: { fontWeight: "bold" },
              }}
            />
          </Stack>
        </SocketProvider>
      </AlertsProvider>
    </InactivityWrapper>
  );
}
