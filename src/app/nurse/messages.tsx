import React from "react";
import { FlatList, Pressable } from "react-native";
import { YStack, Text } from "tamagui";
import { useRouter } from "expo-router";
import { useAlertsContext, Alert } from "src/context/AlertsContext";

const MessageList = () => {
  const router = useRouter();
  const { alerts, loading, error, refreshAlerts } = useAlertsContext();

  if (error) {
    return (
      <YStack
        style={{ alignItems: "center", justifyContent: "center", padding: 20 }}
      >
        <Text style={{ fontSize: 16, color: "red" }}>
          Error loading messages.
        </Text>
      </YStack>
    );
  }

  const renderItem = ({ item }: { item: Alert }) => {
    const { lastMessage } = item;
    const dateTime = new Date(lastMessage.sent_at).toLocaleString();

    return (
      <Pressable
        onPress={() => {
          router.push({
            pathname: "/nurse/chat",
            params: { consultationId: lastMessage.consultation_id },
          });
        }}
      >
        <YStack
          style={{
            padding: 10,
            borderBottomWidth: 1,
            borderColor: "#ccc",
          }}
        >
          <Text style={{ fontSize: 14, color: "#000" }}>{dateTime}</Text>
          <Text
            style={{ fontSize: 16, color: "#000", marginTop: 4 }}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {lastMessage.message}
          </Text>
        </YStack>
      </Pressable>
    );
  };

  return (
    <FlatList
      data={alerts || []}
      keyExtractor={(item) => item.consultationId}
      renderItem={renderItem}
      onRefresh={refreshAlerts}
      refreshing={loading}
      ListEmptyComponent={
        <YStack
          style={{
            alignItems: "center",
            justifyContent: "center",
            padding: 20,
          }}
        >
          <Text style={{ fontSize: 16, color: "#000" }}>
            No unread messages.
          </Text>
        </YStack>
      }
      style={{ flex: 1 }}
    />
  );
};

export default MessageList;
