import { RouteProp, useRoute } from "@react-navigation/native";
import { useNavigation } from "expo-router";
import { useEffect, useState } from "react";
import Consultation from "src/components/Consultation";
import { DialogBox } from "src/components/Dialog";
import { Spinner, Text, View, YStack } from "tamagui";
import { CommonActions } from "@react-navigation/native";
import { useSocket } from "~/context/NurseSocketContext";
import { Modal } from "react-native";
import { useConsultationV2 } from "src/hooks/useCOnsultationV2";
import { ConsultationV2 } from "src/types/consultationV2";

type CalloverViewRouteParams = {
  params: {
    consultationId: string;
  };
};

const CalloverView = () => {
  const { socket } = useSocket();
  const route = useRoute<RouteProp<CalloverViewRouteParams, "params">>();
  const { consultationId } = route.params;
  const [dialogOpen, setDialogOpen] = useState(false);
  const navigation = useNavigation();
  const { consultation: initialConsultation, loading } =
    useConsultationV2(consultationId);
  const [consultation, setConsultation] = useState<ConsultationV2 | null>(null);

  useEffect(() => {
    if (initialConsultation) {
      setConsultation(initialConsultation);
    }
  }, [initialConsultation]);

  useEffect(() => {
    if (!socket || !consultationId) {
      return;
    }

    const handleOrderSubmitted = (data: {
      order: string;
      consultationId: string;
    }) => {
      console.log("Order submitted:", data);
      const { order, consultationId: submittedId } = data;
      if (submittedId === consultationId) {
        setConsultation((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            order,
          };
        });
      }
    };

    socket.on("orderSubmitted", handleOrderSubmitted);

    return () => {
      socket.off("orderSubmitted", handleOrderSubmitted);
    };
  }, [socket]);

  const clearStackAndMove = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: "dashboard" }],
      })
    );
  };

  const onFinishLaterPress = () => {
    setDialogOpen(true);
  };
  const onFinishLaterDialog = () => {
    setDialogOpen(false);
    setTimeout(() => {
      clearStackAndMove();
    }, 100);
  };

  const onClosePress = () => {
    setDialogOpen(false);
    setTimeout(() => {
      clearStackAndMove();
    }, 100);
  };

  if (loading) {
    return (
      <View {...calloverViewStyles.spinner}>
        <Spinner size="large" />
      </View>
    );
  }

  return (
    <View {...calloverViewStyles.screenContainer}>
      <YStack {...calloverViewStyles.mainContainer}>
        <View {...calloverViewStyles.headerContainer}>
          <Text {...calloverViewStyles.headerText}>Call Overview</Text>
        </View>
        {consultation && (
          <View>
            <Consultation
              data={consultation}
              isFromCallScreen={false}
              isFromProvider={false}
              isFromCallOverViewScreen={true}
              onFinishLaterPress={onFinishLaterPress}
              onClosePress={onClosePress}
            />
          </View>
        )}
      </YStack>
      <Modal visible={dialogOpen} transparent animationType="fade">
        <View
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flex: 1,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            height: "100%",
            width: "100%",
          }}
        >
          <DialogBox
            open={dialogOpen}
            onClose={setDialogOpen}
            title="UnConfirmed Order"
            body="Some text here about needing to confirm order within a certain time or cant make other requests"
            btnText="Finish Later"
            onFinishLater={onFinishLaterDialog} // pass it here
          />
        </View>
      </Modal>
    </View>
  );
};

export default CalloverView;

const calloverViewStyles = {
  screenContainer: {
    backgroundColor: "$screenBackgroundcolor",
    flex: 1,
  },
  spinner: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "$screenBackgroundcolor",
  },
  mainContainer: {
    marginBlock: 20,
    marginInline: 20,
    backgroundColor: "$screenBackgroundcolor",
  },
  headerContainer: {
    marginBlockEnd: 20,
    backgroundColor: "$screenBackgroundcolor",
  },
  headerText: {
    fontWeight: 600 as any,
    fontSize: 20 as any,
  },
};
