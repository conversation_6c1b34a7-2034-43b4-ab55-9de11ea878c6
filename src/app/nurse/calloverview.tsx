import { Route<PERSON>rop, useRoute } from "@react-navigation/native";
import { useNavigation } from "expo-router";
import { useEffect, useRef, useState } from "react";
import Consultation from "src/components/Consultation";
import { DialogBox } from "src/components/Dialog";
import { <PERSON><PERSON>, Spinner, Text, TextArea, View, XStack, YStack } from "tamagui";
import { CommonActions } from "@react-navigation/native";
import { useSocket } from "~/context/NurseSocketContext";
import { Keyboard, Modal } from "react-native";
import { useConsultationV2 } from "src/hooks/useCOnsultationV2";
import { ConsultationV2 } from "src/types/consultationV2";
import RatingModule from "src/components/RatingModule";
import { Save } from "@tamagui/lucide-icons";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import axiosConfig from "~/services/axiosConfig";
type CalloverViewRouteParams = {
  params: {
    consultationId: string;
  };
};

const CalloverView = () => {
  const { socket } = useSocket();
  const route = useRoute<RouteProp<CalloverViewRouteParams, "params">>();
  const { consultationId } = route.params;
  const [dialogOpen, setDialogOpen] = useState(false);
  const navigation = useNavigation();
  const { consultation: initialConsultation, loading } =
    useConsultationV2(consultationId);
  const [consultation, setConsultation] = useState<ConsultationV2 | null>(null);
  const scrollViewRef = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const [overallExperienceRating, setOverallExperienceRating] = useState(0);
  const [qualityOfCallRating, setQualityOfCallRating] = useState(0);
  const [providerRating, setProviderRating] = useState(0);
  const [otherFeedback, setOtherFeedback] = useState("");
  const [isEditable, setIsEditable] = useState(true);
  const [submittingRating, setSubmittingRating] = useState(false);

  useEffect(() => {
    if (initialConsultation) {
      setConsultation(initialConsultation);
    }
  }, [initialConsultation]);

  useEffect(() => {
    if (!socket || !consultationId) {
      return;
    }

    const handleOrderSubmitted = (data: {
      order: string;
      consultationId: string;
    }) => {
      console.log("Order submitted:", data);
      const { order, consultationId: submittedId } = data;
      if (submittedId === consultationId) {
        setConsultation((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            order,
          };
        });
      }
    };

    socket.on("orderSubmitted", handleOrderSubmitted);

    return () => {
      socket.off("orderSubmitted", handleOrderSubmitted);
    };
  }, [socket]);

  const clearStackAndMove = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: "dashboard" }],
      })
    );
  };

  const onFinishLaterPress = () => {
    setDialogOpen(true);
  };
  const onFinishLaterDialog = () => {
    setDialogOpen(false);
    setTimeout(() => {
      clearStackAndMove();
    }, 100);
  };

  const onClosePress = () => {
    setDialogOpen(false);
    setTimeout(() => {
      clearStackAndMove();
    }, 100);
  };

  const submitRating = async () => {
    try {
      setSubmittingRating(true);
      const payload: {
        consultationId: string;
        technicalQuality?: number;
        providerCommunication?: number;
        overallSatisfaction?: number;
        comments?: string;
      } = {
        consultationId: "e67174f0-a572-491c-83f8-9f58184105d6",
      };
  
      if (qualityOfCallRating !== 0) {
        payload.technicalQuality = qualityOfCallRating;
      }
      if (providerRating !== 0) {
        payload.providerCommunication = providerRating;
      }
      if (overallExperienceRating !== 0) {
        payload.overallSatisfaction = overallExperienceRating;
      }
      if (otherFeedback && otherFeedback.trim() !== "") {
        payload.comments = otherFeedback;
      }
  
     const result = await axiosConfig.post(`/consultation/rating`, payload);
     if(result.status === 200){
      setIsEditable(false);
     }
    } catch (error) {
      console.log("Error submitting rating: ", error);
    }finally{
      setSubmittingRating(false);
    }
  };
  
  

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        console.log("Keyboard shown");
        if (scrollViewRef.current) {
          setTimeout(() => {
            scrollViewRef.current.scrollToEnd({ animated: true });
          }, 10);
        }
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        console.log("Keyboard hidden");
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleInputFocus = () => {
    if (scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  if (loading || submittingRating) {
    return (
      <View {...calloverViewStyles.spinner}>
        <Spinner size="large" />
      </View>
    );
  }

  return (
    <View {...calloverViewStyles.screenContainer}>
      <YStack {...calloverViewStyles.mainContainer}>
        <View {...calloverViewStyles.headerContainer}>
          <Text {...calloverViewStyles.headerText}>Call Overview</Text>
        </View>

        <KeyboardAwareScrollView
          innerRef={(ref) => (scrollViewRef.current = ref)}
          contentContainerStyle={{ flexGrow: 1 }}
          enableOnAndroid
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {consultation && (
            <View>
              <Consultation
                data={consultation}
                isFromCallScreen={false}
                isFromProvider={false}
                isFromCallOverViewScreen={true}
                onFinishLaterPress={onFinishLaterPress}
                onClosePress={onClosePress}
              />
            </View>
          )}
          <View {...calloverViewStyles.feedbackContainer}>
            <Text {...calloverViewStyles.feedbackText}>Feedback</Text>
            <View {...calloverViewStyles.divider}></View>
            <RatingModule
              title="Overall Experience"
              onRatingChange={(rating) => setOverallExperienceRating(rating)}
              isEditable={isEditable}
            />
            <RatingModule
              title="Quality of call"
              onRatingChange={(rating) => setQualityOfCallRating(rating)}
              isEditable={isEditable}
            />
            <RatingModule
              title="Provider Rating"
              onRatingChange={(rating) => setProviderRating(rating)}
              isEditable={isEditable}
            />
            <Text {...calloverViewStyles.otherText}>Other</Text>
            <XStack {...calloverViewStyles.otherInputContainer} flex={1}>
              <TextArea
                ref={inputRef}
                {...calloverViewStyles.complaintTextArea}
                flex={1}
                onFocus={handleInputFocus}
                onChangeText={setOtherFeedback}
                disabled={!isEditable}
                value={otherFeedback}
              />
              <Button {...calloverViewStyles.saveButton}>
                <Save
                  size={"$1"}
                  color={"$confirmOrderTextColor"}
                  onPress={submitRating}
                />
              </Button>
            </XStack>
          </View>
        </KeyboardAwareScrollView>
      </YStack>
      <Modal visible={dialogOpen} transparent animationType="fade">
        <View
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flex: 1,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            height: "100%",
            width: "100%",
          }}
        >
          <DialogBox
            open={dialogOpen}
            onClose={setDialogOpen}
            title="UnConfirmed Order"
            body="Some text here about needing to confirm order within a certain time or cant make other requests"
            btnText="Finish Later"
            onFinishLater={onFinishLaterDialog}
          />
        </View>
      </Modal>
    </View>
  );
};

export default CalloverView;

const calloverViewStyles = {
  screenContainer: {
    backgroundColor: "$screenBackgroundcolor",
    flex: 1,
  },
  spinner: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "$screenBackgroundcolor",
  },
  mainContainer: {
    marginBlock: 20,
    marginInline: 20,
    backgroundColor: "$screenBackgroundcolor",
  },
  headerContainer: {
    marginBlockEnd: 20,
    backgroundColor: "$screenBackgroundcolor",
  },
  headerText: {
    fontWeight: 600 as any,
    fontSize: 20 as any,
  },
  feedbackContainer: {
    marginBlockStart: 20,
    backgroundColor: "$screenBackgroundcolor" as any,
    borderWidth: 1,
    borderColor: "$primaryBorderColor" as any,
    borderRadius: 10,
    padding: 20,
    marginBlockEnd: 30,
  },
  feedbackText: {
    fontSize: 16,
    fontWeight: 600 as any,
    color: "$textcolor" as any,
    marginBlockStart: 10,
    marginBlockEnd: 15,
  },
  divider: {
    height: 1,
    backgroundColor: "$primaryBorderColor",
  },
  otherText: {
    marginBlockStart: 10,
    marginBlockEnd: 15,
    fontSize: 14,
    fontWeight: 500 as any,
    color: "$textcolor" as any,
  },
  otherInput: {
    placeholder: "Other feedback comments",
    placeholderTextColor: "$textcolor" as any,
    numberOfLines: 1 as any,
    backgroundColor: "$screenBackgroundcolor" as any,
    borderColor: "$primaryBorderColor" as any,
    borderWidth: 1,
  },
  otherInputContainer: {
    justifyContent: "space-between",
    alignItems: "center",
  },
  saveButton: {
    borderWidth: 1,
    padding: 7,
    fontSize: 16,
    size: "$4" as any,
    fontWeight: "600" as any,
    backgroundColor: "$confirmOrderBlue",
    borderColor: "$confirmOrderBorderCOlor" as any,
    color: "$confirmOrderTextColor" as any,
  },
  complaintTextArea: {
    placeholder: "Other feedback comments",
    backgroundColor: "$screenBackgroundcolor" as any,
    borderColor: "$primaryBorderColor" as any,
    size: "$1" as "$1",
    bordeWidth: 1,
    borderRadius: 7,
    color: "$textcolor" as any,
    fontWeight: 500 as any,
    padding: 10,
    fontSize: 16,
    numberOfLines: 4,
    textAlignVertical: "top" as any,
    marginInlineEnd: 10,
    placeholderTextColor: "$textcolor",
  },
};
