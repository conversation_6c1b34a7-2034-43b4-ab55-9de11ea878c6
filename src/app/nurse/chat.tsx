import React from "react";
import { KeyboardAvoidingView, Platform, ScrollView } from "react-native";
import { YStack, Text, Spinner, XStack, Input } from "tamagui";
import {
  useSocket,
  ChatMessage,
  ChatUpdateData,
} from "~/context/NurseSocketContext";
import { useLocalSearchParams, useRouter, useFocusEffect } from "expo-router";
import { useAuth } from "~/context/AuthContext";
import Feather from "@expo/vector-icons/Feather";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import axiosConfig from "~/services/axiosConfig";
import { useTheme } from "@/_layout";
import { useAlertsContext } from "~/context/AlertsContext";

const ChatScreen: React.FC = () => {
  const { socket, onChatUpdate, offChatUpdate } = useSocket();
  const { user } = useAuth();
  const [messages, setMessages] = React.useState<ChatMessage[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [messageInput, setMessageInput] = React.useState<string>("");
  const { consultationId } = useLocalSearchParams();
  const scrollViewRef = React.useRef<ScrollView>(null);
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const { refreshAlerts } = useAlertsContext();
   console.log(consultationId);
  const fetchChatMessages = async () => {
    if (!consultationId) {
      setLoading(false);
      return;
    }
    try {
      setLoading(true);
      const response = await axiosConfig.get(
        `/consultation/chat/${consultationId}`
      );
      const chatData: ChatMessage[] = response.data?.chatMessages ?? [];
      setMessages(chatData);
      if (chatData && chatData.length >= 0) {
        axiosConfig.post(`/consultation/chat/read`, {
          consultationId,
        });
      }
    } catch (error) {
      console.error("Error fetching chat messages:", error);
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchChatMessages();
  }, [consultationId]);

  React.useEffect(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  React.useEffect(() => {
    if (!socket || !consultationId) {
      console.info("Socket or consultationId missing, skipping socket setup");
      return;
    }

    const handleChatUpdate = (data: ChatUpdateData) => {
      const { chatMessage, consultationId: socketConsultationId } = data;
      if (socketConsultationId === consultationId) {
        setMessages((prevMessages) => [...prevMessages, chatMessage]);
        axiosConfig.post(`/consultation/chat/read`, {
          consultationId,
        });
      }
    };

    onChatUpdate(handleChatUpdate);
    return () => {
      offChatUpdate(handleChatUpdate);
    };
  }, [socket, consultationId, onChatUpdate, offChatUpdate]);

  // Handle leaving consultation chat when screen loses focus
  useFocusEffect(
    React.useCallback(() => {
      return () => {
        if (socket && consultationId) {
          refreshAlerts();
          socket.emit("leaveConsultationChat", { consultationId });
        }
      };
    }, [socket, consultationId, refreshAlerts])
  );

  const sendMessage = async () => {
    if (!messageInput.trim() || !consultationId) return;
    try {
      const response = await axiosConfig.post("/consultation/chat", {
        consultationId,
        message: messageInput,
      });
      setMessages((prevMessages) => [
        ...prevMessages,
        response.data.chatMessage,
      ]);
      setMessageInput("");
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: "#fff" }}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 40 : 0}
    >
      <YStack {...styles.container}>
        <YStack {...styles.headerContainer}>
          <ScreenHeader
            onAvatarPress={openSettings}
            screenName="Back"
            onBackPress={navigateBack}
          />
        </YStack>
        {loading ? (
          <YStack {...styles.spinnerContainer}>
            <Spinner size="large" color="$primaryColor" />
          </YStack>
        ) : (
          <ScrollView
            ref={scrollViewRef}
            {...styles.scrollView}
            contentContainerStyle={styles.scrollViewContent}
          >
            <YStack {...styles.messageContainer}>
              {!messages || messages?.length === 0 ? (
                <YStack
                  style={{
                    flex: 1,
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: 150,
                  }}
                >
                  <Feather name="message-square" size={50} color="#ccc" />
                  <Text style={{ fontSize: 18, color: "#ccc", marginTop: 10 }}>
                    No messages yet
                  </Text>
                </YStack>
              ) : (
                messages.map((message) => {
                  const isOutgoing = message.sender_id === user?.id;
                  return (
                    <XStack
                      key={message.id}
                      justify={isOutgoing ? "flex-end" : "flex-start"}
                    >
                      <Text
                        {...(isOutgoing
                          ? styles.outgoingMessage
                          : styles.incomingMessage)}
                      >
                        {message.message}
                      </Text>
                    </XStack>
                  );
                })
              )}
            </YStack>
          </ScrollView>
        )}
        <YStack {...styles.inputContainer}>
          <XStack {...styles.inputText}>
            <Input
              {...styles.input}
              value={messageInput}
              onChangeText={setMessageInput}
              placeholder="Type message"
              placeholderTextColor="$primaryBorderColor"
              onSubmitEditing={sendMessage}
            />
            <Feather
              name={"send"}
              size={24}
              color={isDarkMode ? "#697586" : "black"}
              onPress={sendMessage}
            />
          </XStack>
        </YStack>
        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
    </KeyboardAvoidingView>
  );
};

export default ChatScreen;

const styles = {
  headerContainer: {
    marginBlockStart: 20,
    marginInline: 20,
  },
  spinnerContainer: {
    justifyContent: "center" as any,
    alignItems: "center" as any,
    flex: 1,
  },
  container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
  scrollView: { flex: 1 },
  scrollViewContent: { padding: 20, paddingBottom: 80 },
  messageContainer: { space: "$2" as any, marginBlockEnd: 20 },
  inputContainer: {
    position: "absolute" as any,
    bottom: 0,
    left: 0,
    right: 0,
    padding: "$4",
    backgroundColor: "$screenBackgroundcolor" as any,
    borderColor: "$primaryBorderColor" as any,
    flex: 1,
  },
  input: {
    flex: 1,
    padding: "$3",
    borderRadius: "$3",
    borderWidth: 1,
    borderColor: "$primaryBorderColor" as any,
    backgroundColor: "$screenBackgroundcolor" as any,
  },
  outgoingMessage: {
    padding: "$4",
    borderRadius: "$3",
    backgroundColor: "$chatOutgoingMessageColor" as any,
    color: "black" as any,
    maxWidth: "80%",
    borderTopRightRadius: 0,
    borderTopLeftRadius: 10,
    borderBottomRightRadius: 10,
    borderBottomLeftRadius: 10,
    borderColor: "$chatBorderColor" as any,
    borderWidth: 1,
  },
  incomingMessage: {
    padding: "$4",
    borderRadius: "$3",
    borderColor: "$chatBorderColor" as any,
    borderWidth: 1,
    backgroundColor: "$chatIncomingMessageColor" as any,
    color: "black" as any,
    maxWidth: "80%",
    borderTopRightRadius: 10,
    borderTopLeftRadius: 0,
    borderBottomRightRadius: 10,
    borderBottomLeftRadius: 10,
  },
  inputText: {
    alignItems: "center" as any,
    space: "$2" as any,
  },
};
