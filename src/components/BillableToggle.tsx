import React from "react";
import { TouchableOpacity } from "react-native";
import { View, Text, XStack } from "tamagui";

type BillableToggleProps = {
  isBillable: boolean;
  onToggle: (isBillable: boolean) => void;
  disabled?: boolean;
};

const BillableToggle = ({ isBillable  , onToggle, disabled = false }: BillableToggleProps) => {
  return (
    <View style={toggleStyles.container}>
      <XStack style={toggleStyles.toggleContainer}>
        <TouchableOpacity
          style={[
            toggleStyles.option,
            toggleStyles.leftOption,
            isBillable && toggleStyles.activeOption,
          ]}
          onPress={() => !disabled && onToggle(true)}
          disabled={disabled}
        >
          <Text
            style={[
              toggleStyles.optionText,
              isBillable && toggleStyles.activeOptionText,
            ]}
          >
            Billable
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            toggleStyles.option,
            toggleStyles.rightOption,
            !isBillable && toggleStyles.activeOption,
          ]}
          onPress={() => !disabled && onToggle(false)}
          disabled={disabled}
        >
          <Text
            style={[
              toggleStyles.optionText,
              !isBillable && toggleStyles.activeOptionText,
            ]}
          >
            Non Billable
          </Text>
        </TouchableOpacity>
      </XStack>
    </View>
  );
};

const toggleStyles = {
  container: {
    alignItems: "center" as const,
    justifyContent: "center" as const,
  },
  toggleContainer: {
    backgroundColor: "#1570ef",
    borderRadius: 8, 
    padding: 2,
    alignItems: "center" as const,
    justifyContent: "center" as const,
    height: 44,
  },
  option: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: "center" as const,
    justifyContent: "center" as const,
    flex: 1,
    height: 40,
  },
  leftOption: {
    borderTopLeftRadius: 6,
    borderBottomLeftRadius: 6,
  },
  rightOption: {
    borderTopRightRadius: 6,
    borderBottomRightRadius: 6,
  },
  activeOption: {
    backgroundColor: "#FFFFFF",
  },
  optionText: {
    fontSize: 16,
    fontWeight: "600" as const,
    color: "#FFFFFF",
  },
  activeOptionText: {
    color: "#8B5CF6",
  },
};

export default BillableToggle;
