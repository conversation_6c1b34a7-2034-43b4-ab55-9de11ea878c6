import React from "react";
import { TouchableOpacity, StyleSheet } from "react-native";
import { View, Text } from "tamagui";

type BillableToggleProps = {
  isBillable: boolean;
  onToggle: (isBillable: boolean) => void;
  disabled?: boolean;
};

const BillableToggle = ({ isBillable, onToggle, disabled = false }: BillableToggleProps) => {
  console.log("BillableToggle rendered with isBillable:", isBillable);

  return (
    <View style={styles.container}>
      <View style={styles.toggleContainer}>
        <TouchableOpacity
          style={[
            styles.option,
            styles.leftOption,
            isBillable && styles.activeOption,
          ]}
          onPress={() => !disabled && onToggle(true)}
          disabled={disabled}
        >
          <Text
            style={[
              styles.optionText,
              isBillable && styles.activeOptionText,
            ]}
          >
            Billable
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.option,
            styles.rightOption,
            !isBillable && styles.activeOption,
          ]}
          onPress={() => !disabled && onToggle(false)}
          disabled={disabled}
        >
          <Text
            style={[
              styles.optionText,
              !isBillable && styles.activeOptionText,
            ]}
          >
            Non Billable
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  toggleContainer: {
    backgroundColor: "#8B5CF6",
    borderRadius: 8,
    padding: 2,
    alignItems: "center",
    justifyContent: "center",
    height: 44,
    flexDirection: "row",
    minWidth: 200,
  },
  option: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    height: 40,
  },
  leftOption: {
    borderTopLeftRadius: 6,
    borderBottomLeftRadius: 6,
  },
  rightOption: {
    borderTopRightRadius: 6,
    borderBottomRightRadius: 6,
  },
  activeOption: {
    backgroundColor: "#FFFFFF",
    borderRadius: 6,
  },
  optionText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    textAlign: "center",
  },
  activeOptionText: {
    color: "#8B5CF6",
  },
});

export default BillableToggle;