import React from "react";
import { TouchableOpacity } from "react-native";
import { View, Text } from "tamagui";

type BillableToggleProps = {
  isBillable: boolean;
  onToggle: (isBillable: boolean) => void;
  disabled?: boolean;
};

const BillableToggle = ({ isBillable, onToggle, disabled = false }: BillableToggleProps) => {
  console.log("BillableToggle rendered with isBillable:", isBillable);

  const containerStyle = {
    alignItems: "center" as any,
    justifyContent: "center" as any,
  };

  const toggleContainerStyle = {
    backgroundColor: "$primaryColor",
    borderRadius: "$3" as any,
    padding: "$1" as any,
    alignItems: "center" as any,
    justifyContent: "center" as any,
    height: "$4" as any,
    flexDirection: "row" as any,
    width: "100%" as any,
    paddingInline: 5,
  };

  const leftOptionStyle = {
    paddingHorizontal: 0,
    paddingVertical: 8,
    alignItems: "center" as any,
    justifyContent: "center" as any,
    flex: 1,
    borderTopLeftRadius: 6,
    borderBottomLeftRadius: 6,
    backgroundColor: isBillable ? "#FFFFFF" : "transparent",
    borderRadius: isBillable ? 6 : 0,
  };

  const rightOptionStyle = {
    paddingHorizontal: 0,
    paddingVertical: 8,
    alignItems: "center" as any,
    justifyContent: "center" as any,
    flex: 1,
    borderTopRightRadius: 6,
    borderBottomRightRadius: 6,
    backgroundColor: !isBillable ? "#FFFFFF" : "transparent",
    borderRadius: !isBillable ? 6 : 0,
  };

  const leftTextStyle = {
    fontSize: "$4" as any,
    fontWeight: "600" as any,
    color: (isBillable ? "$primaryColor" : "$screenBackgroundcolor") as any,
    textAlign: "center" as any,
  };

  const rightTextStyle = {
    fontSize: "$4" as any,
    fontWeight: "600" as any,
    color: (!isBillable ? "$primaryColor" : "$screenBackgroundcolor") as any,
    textAlign: "center" as any,
  };

  return (
    <View {...containerStyle}>
      <View {...toggleContainerStyle}>
        <TouchableOpacity
          style={leftOptionStyle}
          onPress={() => !disabled && onToggle(true)}
          disabled={disabled}
        >
          <Text {...leftTextStyle}>
            Billable
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={rightOptionStyle}
          onPress={() => !disabled && onToggle(false)}
          disabled={disabled}
        >
          <Text {...rightTextStyle}>
            Non Billable
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default BillableToggle;